package com.facishare.crm.sfa.predefine.action;

import com.facishare.crm.constants.SystemConstants;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.lto.objectlimit.ObjectLimitService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.AccountPathSynchronizer;
import com.facishare.crm.sfa.predefine.service.SFAEnterpriseInfoService;
import com.facishare.crm.sfa.predefine.service.qywx.EnterpriseWechatService;
import com.facishare.crm.sfa.task.RelationOperationType;
import com.facishare.crm.sfa.utilities.constant.AccountAddrConstants;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.constant.AccountFinInfoConstants;
import com.facishare.crm.sfa.utilities.util.*;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppIdMapping;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.appframework.prm.util.PrmConstant;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.Strings;

import java.util.*;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF;
import static com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil.*;

/**
 * 客户编辑操作
 * <p>
 * Created by liyiguang on 2017/7/13.
 */
@Slf4j
public class AccountEditAction extends StandardEditAction {
    private static final SFAEnterpriseInfoService enterpriseInfoService = SpringUtil.getContext().getBean(SFAEnterpriseInfoService.class);
    private static final ObjectLimitService objectLimitService = SpringUtil.getContext().getBean(ObjectLimitService.class);
	protected final EnterpriseWechatService enterpriseWechatService = SpringUtil.getContext().getBean(EnterpriseWechatService.class);

    private static final List<String> editExceptFields = Lists.newArrayList(SystemConstants.Field.Owner.apiName
            , AccountConstants.Field.EXPIRE_TIME, AccountConstants.Field.LAST_FOLLOWED_TIME
            , AccountConstants.Field.OWNER_MODIFIED_TIME, AccountConstants.Field.REMIND_DAYS
            , AccountConstants.Field.DEAL_STATUS, AccountConstants.Field.LAST_DEAL_TIME, AccountConstants.Field.FIELD_ACCOUNT_PATH,AccountConstants.Field.BIZ_STATUS);
    private boolean isNeedChangeAccountPath = false;
    private boolean isNeedChangeAccountAddr = true;
    private boolean isChangeLeadsId = false;
    private boolean syncEnterprise = false;
    private Map<String, Map<String,Object>> needUpdateInheritField = new HashMap<>();

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        //获取手机归属地字段
        AccountUtil.getPhoneNumberInfo(arg.getObjectData(), AccountConstants.Field.TEL,dbMasterData);
        ObjectDataDocument dataDocument = arg.getObjectData();
        String name = AccountUtil.getStringValue(dataDocument, "name", "");
        if (StringUtils.isNotBlank(name)) {
            String pinyinString = Chinese2PinyinUtils.getPinyinString(name);
            dataDocument.put("pin_yin", pinyinString);
        }

        Object objectValue = dataDocument.get(AccountConstants.Field.RECORD_TYPE);
        if (objectValue != null && !objectData.getRecordType().equals(objectValue.toString())) {
                throw new ValidateException(I18N.text(SFA_CANNOT_CHANGE_RECORDTYPE));
        }

        String oldLeadsId = AccountUtil.getStringValue(dbMasterData, "leads_id", "");
        if (StringUtils.isNotBlank(oldLeadsId)) {
            dataDocument.remove("leads_id");
        } else {
            String newLeadsId = AccountUtil.getStringValue(dataDocument, "leads_id", "");
            if (StringUtils.isNotBlank(newLeadsId)) {
                AccountUtil.checkChangeLeadsId(actionContext.getUser(), objectDescribe, newLeadsId, arg.getObjectData().getId());
                isChangeLeadsId = true;
            }
        }

        if (dataDocument.containsKey(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID)) {
            String parentAccountId = dataDocument.toObjectData().get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
            String oldParentAccountId = CollectionUtils.empty(dataList) ? "" : dataList.get(0).get(AccountConstants.Field.FIELD_PARENT_ACCOUNT_ID, String.class);
            if (Objects.equals(parentAccountId, dataDocument.toObjectData().getId())) {
                throw new ValidateException(FS_CRM_DEFOBJ_CHECKED_CAN_NOT_LOOKUP_MYSELF.getMessage());
            }
            if (!Objects.equals(parentAccountId, oldParentAccountId)) {
                if (!Strings.isNullOrEmpty(parentAccountId)) {
                    AccountPathUtil.checkIsHoop(actionContext.getUser(), Lists.newArrayList(dataDocument.toObjectData()));
                }
                isNeedChangeAccountPath = true;
            }
        }

        int completedFieldQuantity = AccountUtil.calculateObjectHasValueCount(objectDescribe, dataDocument.toObjectData());
        dataDocument.put("completed_field_quantity", completedFieldQuantity);
        if (objectLimitService.isGrayObjectLimit(actionContext.getTenantId())) {
            List<IObjectData> oldDataList = ObjectDataExt.copyList(dataList);
            List<IObjectData> checkLimitDataList = ObjectDataExt.copyList(Lists.newArrayList(dataDocument.toObjectData()));
            checkLimitDataList.forEach(x -> {
                x.set(SystemConstants.Field.LifeStatus.apiName, ObjectLifeStatus.NORMAL.getCode());
                x.setLastModifiedBy(actionContext.getUser().getUpstreamOwnerIdOrUserId());
                x.setLastModifiedTime(System.currentTimeMillis());
            });
            if (AppIdMapping.appIdMapping.get(PrmConstant.PRM_APP_ID).equals(actionContext.getAppId())) {
                String outOwner = AccountUtil.getOutOwner(dbMasterData);
                String outTenantId = AccountUtil.getStringValue(dbMasterData, SystemConstants.Field.OutTenantId.apiName, "");
                if (StringUtils.isNotBlank(outOwner) && StringUtils.isNotBlank(outTenantId)) {
                    ObjectLimitService.CheckLimitResult checkOutLimitResult = objectLimitService.checkOutUserObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), outTenantId, outOwner, oldDataList, checkLimitDataList, objectDescribe, true);
                    if (CollectionUtils.notEmpty(checkOutLimitResult.getFailureIds())) {
                        throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                                I18N.text("AccountObj.attribute.self.display_name")));
                    }
                }
            }

            String owner = CommonBizUtils.getOwner(dbMasterData);
            if (StringUtils.isNotBlank(owner)) {
                ObjectLimitService.CheckLimitResult checkLimitResult = objectLimitService.checkObjectLimitForEdit(actionContext.getUser(), actionContext.getObjectApiName(), owner, oldDataList, checkLimitDataList, objectDescribe);
                if (CollectionUtils.notEmpty(checkLimitResult.getFailureIds())) {
                    for (String failureId : checkLimitResult.getFailureIds()) {
                        if (!checkLimitResult.getFailureIdAndRuleName().isEmpty() && checkLimitResult.getFailureIdAndRuleName().get(failureId) != null) {
                            throw new ValidateException(String.format(I18N.text(SFA_REACH_RULE_LIMIT_OBJ), checkLimitResult.getFailureIdAndRuleName().get(failureId),
                                    I18N.text("AccountObj.attribute.self.display_name")));
                        }
                    }
                    throw new ValidateException(String.format(I18N.text(SFA_REACH_LIMIT_OBJ),
                            I18N.text("AccountObj.attribute.self.display_name")));
                }
            }
        }
        boolean oldBizRegName = AccountUtil.getBooleanValue(dbMasterData, "biz_reg_name", false);
        boolean bizRegName = AccountUtil.getBooleanValue(dataDocument, "biz_reg_name", false);
        if (oldBizRegName != bizRegName || !name.equals(dbMasterData.getName())) {
            syncEnterprise = true;
        }
        for (String field : editExceptFields) {
            dataDocument.remove(field);
        }
		enterpriseWechatService.fillRelatedWechatWorkExternalUserId(actionContext.getUser(), objectDescribe, Lists.newArrayList(objectData));
        needUpdateInheritField = AccountUtil.handleNeedUpdateInheritField(objectDescribe,Lists.newArrayList(arg.getObjectData().toObjectData()),actionContext);
        isNeedChangeAccountAddr = AccountUtil.checkIsNeedUpdMainAddr(actionContext.getUser(),arg.getObjectData(),dbMasterData);
        AccountUtil.replaceInheritFieldsValue(objectDescribe,arg.getObjectData(),dbMasterData);
    }

    @Override
    protected void validateDetail(String detailApiName, List<IObjectData> detailDataList) {
        if (RequestUtil.isH5Request()) {
            return;
        }
        super.validateDetail(detailApiName, detailDataList);
        if (CollectionUtils.empty(detailDataList)) {
            return;
        }
        if (Utils.ACCOUNT_ADDR_API_NAME.equals(detailApiName)) {
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_765)) {
                throw new SFABusinessException(SFAErrorCode.CLIENT_UPGRADE_PROMPT);
            }
            isNeedChangeAccountAddr = false;
            if(AccountUtil.isOpenAccountAddrConfig(actionContext.getUser())){
                log.warn("acountEdit isOpenAccountAddrConfig is true");
                AccountAddrUtil.handleCheckManyMainAddr(actionContext.getUser(),detailDataList,new HashMap<>());
            }else{
                detailDataList.forEach(m -> {
                    AccountAddrUtil.handleLocationField(m);
                    AccountAddrUtil.handleGeoPointField(m);
                    ObjectDataDocument accountAddr = ObjectDataDocument.of(m);
                    accountAddr.remove(AccountAddrConstants.Field.IS_DEFAULT_ADD.getApiName());
                    accountAddr.remove(AccountAddrConstants.Field.IS_SHIP_TO_ADD.getApiName());
                });
            }

        } else if (Utils.ACCOUNT_FIN_INFO_API_NAME.equals(detailApiName)) {
            detailDataList.forEach(m -> {
                ObjectDataDocument accountFinInfo = ObjectDataDocument.of(m);
                accountFinInfo.remove(AccountFinInfoConstants.Field.IS_DEFAULT.getApiName());
            });
        }
    }

    @Override
    protected List<DuplicateSearchResult.DuplicateData> getDuplicateData() {
        List<DuplicateSearchResult.DuplicateData> result = super.getDuplicateData();
        LeadsTransferLogUtil.dealDuplicatedData(actionContext.getUser(), actionContext.getObjectApiName(), objectData.getId(), result);
        return result;
    }

    @Override
    protected Set<String> getIgnoreFieldsForApproval() {
        Set<String> result = super.getIgnoreFieldsForApproval();
        result.add(AccountConstants.Field.EXPIRE_TIME);
        result.add(AccountConstants.Field.REMIND_DAYS);
        result.add(AccountConstants.Field.LAST_FOLLOWED_TIME);
        result.add(AccountConstants.Field.LAST_DEAL_TIME);
        return result;
    }

    @Override
    protected Result after(Arg arg, Result result) {
        //成功触发审批流不需要执行后续操作
        stopWatch.lap("AccountEditAction.after-1");
        if (isApprovalFlowStartSuccess(this.objectData.getId())) {
            //如果是未生效的数据，编辑触发的审批流，则保存地址
            if(UdobjConstants.LIFE_STATUS_VALUE_UNDER_REVIEW.equals(this.objectData.get(UdobjConstants.LIFE_STATUS_API_NAME,String.class))){
                AccountAddrUtil.updateAccountAddrForMain(actionContext.getUser(), result.getObjectData().toObjectData());
            }
            AccountPathUtil.dealAccountPathToMQ(actionContext.getUser(), Sets.newHashSet(this.objectData.getId()));
            return result;
        }
        stopWatch.lap("AccountEditAction.after-2");
        result = super.after(arg, result);
        stopWatch.lap("AccountEditAction.after-3");
        if (isNeedChangeAccountPath) {
            AccountPathSynchronizer.builder()
                    .user(actionContext.getUser())
                    .objectDataList(Lists.newArrayList(result.getObjectData().toObjectData()))
                    .build()
                    .asyncDealData();
        }

        if (isNeedChangeAccountAddr) {
            //修改客户时，同时修改客户地址中的主地址
            AccountAddrUtil.updateAccountAddrForMain(actionContext.getUser(), result.getObjectData().toObjectData());
        }

        if (syncEnterprise) {
            enterpriseInfoService.createOrRelateEnterpriseAsync(actionContext.getRequestContext(), actionContext.getObjectApiName(), result.getObjectData().toObjectData());
        }

        if (isChangeLeadsId) {
            String accountId = arg.getObjectData().getId();
            String leadsId = AccountUtil.getStringValue(arg.getObjectData(), "leads_id", "");
            if (StringUtils.isNotBlank(leadsId) && StringUtils.isNotBlank(accountId)) {
                LeadsTransferLogUtil.updateLeadsTransferLog(actionContext.getUser(), leadsId, accountId);
            }
        }
        AccountUtil.updataMainDataByControlStrategy(objectDescribe,needUpdateInheritField,Lists.newArrayList(result.getObjectData().toObjectData()),dbMasterData,actionContext);
        AccountUtil.sendHandleRelationshiMsgByIds(actionContext.getTenantId(),Lists.newArrayList(objectData.getId()), RelationOperationType.EDIT.getValue());
        stopWatch.lap("AccountEditAction.after-4");
        return result;
    }
}
